0000000000000000000000000000000000000000 5314190432f740a97dfddb74d077ff3c2b9ef20c Sov3rain <<EMAIL>> 1758117636 +0200	branch: Created from HEAD
5314190432f740a97dfddb74d077ff3c2b9ef20c c8f33d99e116351a20782078aa8b1314c74fa7d1 Sov3rain <<EMAIL>> 1758117647 +0200	commit: chore: add unit tests for `ExtensionFilter` and `StandaloneFileBrowser`, include README for test suite and test assembly definition
c8f33d99e116351a20782078aa8b1314c74fa7d1 d2b7eecc3c0642be8a6e2fe1a5e6d84f4437adaf Sov3rain <<EMAIL>> 1758118334 +0200	commit: chore: make `ExtensionFilter` fields readonly, handle null extensions with `Array.Empty<string>()`
